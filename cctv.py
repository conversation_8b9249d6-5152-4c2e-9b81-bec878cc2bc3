import cv2
import requests
import time

# Your server endpoint
url = 'http://localhost:8080/api/cctv/snapshot'
camera_id = 'camera-001'

# Load the video file
video = cv2.VideoCapture("sample.mp4")

fps = video.get(cv2.CAP_PROP_FPS)
frame_interval = int(fps * 5)  # every 5 seconds

frame_count = 0

while video.isOpened():
    ret, frame = video.read()
    if not ret:
        break

    if frame_count % frame_interval == 0:
        _, img_encoded = cv2.imencode('.jpg', frame)
        response = requests.post(
            url,
            files={'image': ('snapshot.jpg', img_encoded.tobytes(), 'image/jpeg')},
            data={'camera_id': camera_id}
        )
        print("Sent frame at", frame_count // fps, "seconds:", response.status_code)
        time.sleep(0.1)  # small delay to simulate real send

    frame_count += 1

video.release()
